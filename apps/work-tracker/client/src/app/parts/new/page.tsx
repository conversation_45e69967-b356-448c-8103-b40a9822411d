"use client";

import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import PartsForm from "@/features/parts/components/form/PartsForm";
import useTitle from "@/hooks/useTitle";
import { PartFormData } from "@/features/parts/lib/validations";
import { usePartsCrud } from "@/features/parts/hooks/useParts";

export default function NewPartPage() {
    useTitle("Create New Part");
    const router = useRouter();
    const { createPart, isLoading, getLatestPartId } = usePartsCrud();
    const [latestId, setLatestId] = useState<number | null>(null);
    const [isCheckingId, setIsCheckingId] = useState(true);

    // Handle form submission
    const handleSubmit = async (data: PartFormData) => {
        try {
            // Check latest ID again before submitting
            const currentLatestId = await getLatestPartId();
            console.log("Latest ID before creating new part:", currentLatestId);

            await createPart(data);
        } catch (error) {
            // Error is already handled in the hook
        }
    };

    // Handle cancel button
    const handleCancel = () => {
        router.push("/parts");
    };

    return (
        <main className="w-full max-w-6xl p-8 mx-auto flex flex-col">
            <h1 className="font-semibold text-3xl pb-12">Create New Part</h1>

            {isCheckingId ? (
                <div className="mb-4">Checking latest part ID...</div>
            ) : (
                <div className="mb-4">
                    Latest part ID:{" "}
                    {latestId !== null ? latestId : "No parts found"}
                </div>
            )}

            <div className="w-full max-w-6xl">
                <PartsForm
                    onSubmit={handleSubmit}
                    isSubmitting={isLoading}
                    onCancel={handleCancel}
                />
            </div>
        </main>
    );
}
